{"version": 3, "names": ["_sourceMap", "require", "_printer", "Generator", "Printer", "constructor", "ast", "opts", "code", "format", "normalizeOptions", "map", "sourceMaps", "SourceMap", "generate", "_opts$recordAndTupleS", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "shouldPrintComment", "retainLines", "retainFunctionParens", "comments", "compact", "minified", "concise", "indent", "adjustMultilineComment", "style", "jsescOption", "Object", "assign", "quotes", "wrap", "minimal", "recordAndTupleSyntaxType", "topicToken", "importAttributesKeyword", "decoratorsBeforeExport", "json", "jsonCompatibleStrings", "value", "includes", "length", "console", "error", "filename", "undefined", "CodeGenerator", "_generator", "exports", "gen"], "sources": ["../src/index.ts"], "sourcesContent": ["import SourceMap from \"./source-map.ts\";\nimport Printer from \"./printer.ts\";\nimport type * as t from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\nimport type { Format } from \"./printer.ts\";\nimport type {\n  RecordAndTuplePluginOptions,\n  PipelineOperatorPluginOptions,\n} from \"@babel/parser\";\nimport type {\n  EncodedSourceMap,\n  DecodedSourceMap,\n  Mapping,\n} from \"@jridgewell/gen-mapping\";\n\n/**\n * <PERSON><PERSON>'s code generator, turns an ast into code, maintaining sourcemaps,\n * user preferences, and valid output.\n */\n\nclass Generator extends Printer {\n  constructor(\n    ast: t.Node,\n    opts: GeneratorOptions = {},\n    code: string | { [filename: string]: string },\n  ) {\n    const format = normalizeOptions(code, opts);\n    const map = opts.sourceMaps ? new SourceMap(opts, code) : null;\n    super(format, map);\n\n    this.ast = ast;\n  }\n\n  ast: t.Node;\n\n  /**\n   * Generate code and sourcemap from ast.\n   *\n   * Appends comments that weren't attached to any node to the end of the generated output.\n   */\n\n  generate() {\n    return super.generate(this.ast);\n  }\n}\n\n/**\n * Normalize generator options, setting defaults.\n *\n * - Detects code indentation.\n * - If `opts.compact = \"auto\"` and the code is over 500KB, `compact` will be set to `true`.\n */\n\nfunction normalizeOptions(\n  code: string | { [filename: string]: string },\n  opts: GeneratorOptions,\n): Format {\n  const format: Format = {\n    auxiliaryCommentBefore: opts.auxiliaryCommentBefore,\n    auxiliaryCommentAfter: opts.auxiliaryCommentAfter,\n    shouldPrintComment: opts.shouldPrintComment,\n    retainLines: opts.retainLines,\n    retainFunctionParens: opts.retainFunctionParens,\n    comments: opts.comments == null || opts.comments,\n    compact: opts.compact,\n    minified: opts.minified,\n    concise: opts.concise,\n    indent: {\n      adjustMultilineComment: true,\n      style: \"  \",\n    },\n    jsescOption: {\n      quotes: \"double\",\n      wrap: true,\n      minimal: process.env.BABEL_8_BREAKING ? true : false,\n      ...opts.jsescOption,\n    },\n    recordAndTupleSyntaxType: opts.recordAndTupleSyntaxType ?? \"hash\",\n    topicToken: opts.topicToken,\n    importAttributesKeyword: opts.importAttributesKeyword,\n  };\n\n  if (!process.env.BABEL_8_BREAKING) {\n    format.decoratorsBeforeExport = opts.decoratorsBeforeExport;\n    format.jsescOption.json = opts.jsonCompatibleStrings;\n  }\n\n  if (format.minified) {\n    format.compact = true;\n\n    format.shouldPrintComment =\n      format.shouldPrintComment || (() => format.comments);\n  } else {\n    format.shouldPrintComment =\n      format.shouldPrintComment ||\n      (value =>\n        format.comments ||\n        value.includes(\"@license\") ||\n        value.includes(\"@preserve\"));\n  }\n\n  if (format.compact === \"auto\") {\n    format.compact = typeof code === \"string\" && code.length > 500_000; // 500KB\n\n    if (format.compact) {\n      console.error(\n        \"[BABEL] Note: The code generator has deoptimised the styling of \" +\n          `${opts.filename} as it exceeds the max of ${\"500KB\"}.`,\n      );\n    }\n  }\n\n  if (format.compact) {\n    format.indent.adjustMultilineComment = false;\n  }\n\n  const { auxiliaryCommentBefore, auxiliaryCommentAfter, shouldPrintComment } =\n    format;\n\n  if (auxiliaryCommentBefore && !shouldPrintComment(auxiliaryCommentBefore)) {\n    format.auxiliaryCommentBefore = undefined;\n  }\n  if (auxiliaryCommentAfter && !shouldPrintComment(auxiliaryCommentAfter)) {\n    format.auxiliaryCommentAfter = undefined;\n  }\n\n  return format;\n}\n\nexport interface GeneratorOptions {\n  /**\n   * Optional string to add as a block comment at the start of the output file.\n   */\n  auxiliaryCommentBefore?: string;\n\n  /**\n   * Optional string to add as a block comment at the end of the output file.\n   */\n  auxiliaryCommentAfter?: string;\n\n  /**\n   * Function that takes a comment (as a string) and returns true if the comment should be included in the output.\n   * By default, comments are included if `opts.comments` is `true` or if `opts.minified` is `false` and the comment\n   * contains `@preserve` or `@license`.\n   */\n  shouldPrintComment?(comment: string): boolean;\n\n  /**\n   * Attempt to use the same line numbers in the output code as in the source code (helps preserve stack traces).\n   * Defaults to `false`.\n   */\n  retainLines?: boolean;\n\n  /**\n   * Retain parens around function expressions (could be used to change engine parsing behavior)\n   * Defaults to `false`.\n   */\n  retainFunctionParens?: boolean;\n\n  /**\n   * Should comments be included in output? Defaults to `true`.\n   */\n  comments?: boolean;\n\n  /**\n   * Set to true to avoid adding whitespace for formatting. Defaults to the value of `opts.minified`.\n   */\n  compact?: boolean | \"auto\";\n\n  /**\n   * Should the output be minified. Defaults to `false`.\n   */\n  minified?: boolean;\n\n  /**\n   * Set to true to reduce whitespace (but not as much as opts.compact). Defaults to `false`.\n   */\n  concise?: boolean;\n\n  /**\n   * Used in warning messages\n   */\n  filename?: string;\n\n  /**\n   * Enable generating source maps. Defaults to `false`.\n   */\n  sourceMaps?: boolean;\n\n  inputSourceMap?: any;\n\n  /**\n   * A root for all relative URLs in the source map.\n   */\n  sourceRoot?: string;\n\n  /**\n   * The filename for the source code (i.e. the code in the `code` argument).\n   * This will only be used if `code` is a string.\n   */\n  sourceFileName?: string;\n\n  /**\n   * Set to true to run jsesc with \"json\": true to print \"\\u00A9\" vs. \"©\";\n   * @deprecated use `jsescOptions: { json: true }` instead\n   */\n  jsonCompatibleStrings?: boolean;\n\n  /**\n   * Set to true to enable support for experimental decorators syntax before\n   * module exports. If not specified, decorators will be printed in the same\n   * position as they were in the input source code.\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n\n  /**\n   * Options for outputting jsesc representation.\n   */\n  jsescOption?: jsescOptions;\n\n  /**\n   * For use with the recordAndTuple token.\n   */\n  recordAndTupleSyntaxType?: RecordAndTuplePluginOptions[\"syntaxType\"];\n\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: PipelineOperatorPluginOptions[\"topicToken\"];\n\n  /**\n   * The import attributes syntax style:\n   * - \"with\"        : `import { a } from \"b\" with { type: \"json\" };`\n   * - \"assert\"      : `import { a } from \"b\" assert { type: \"json\" };`\n   * - \"with-legacy\" : `import { a } from \"b\" with type: \"json\";`\n   */\n  importAttributesKeyword?: \"with\" | \"assert\" | \"with-legacy\";\n}\n\nexport interface GeneratorResult {\n  code: string;\n  map: EncodedSourceMap | null;\n  decodedMap: DecodedSourceMap | undefined;\n  rawMappings: Mapping[] | undefined;\n}\n\n/**\n * We originally exported the Generator class above, but to make it extra clear that it is a private API,\n * we have moved that to an internal class instance and simplified the interface to the two public methods\n * that we wish to support.\n */\n\nexport class CodeGenerator {\n  private _generator: Generator;\n  constructor(ast: t.Node, opts?: GeneratorOptions, code?: string) {\n    this._generator = new Generator(ast, opts, code);\n  }\n  generate(): GeneratorResult {\n    return this._generator.generate();\n  }\n}\n\n/**\n * Turns an AST into code, maintaining sourcemaps, user preferences, and valid output.\n * @param ast - the abstract syntax tree from which to generate output code.\n * @param opts - used for specifying options for code generation.\n * @param code - the original source code, used for source maps.\n * @returns - an object containing the output code and source map.\n */\nexport default function generate(\n  ast: t.Node,\n  opts?: GeneratorOptions,\n  code?: string | { [filename: string]: string },\n) {\n  const gen = new Generator(ast, opts, code);\n  return gen.generate();\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAmBA,MAAME,SAAS,SAASC,gBAAO,CAAC;EAC9BC,WAAWA,CACTC,GAAW,EACXC,IAAsB,GAAG,CAAC,CAAC,EAC3BC,IAA6C,EAC7C;IACA,MAAMC,MAAM,GAAGC,gBAAgB,CAACF,IAAI,EAAED,IAAI,CAAC;IAC3C,MAAMI,GAAG,GAAGJ,IAAI,CAACK,UAAU,GAAG,IAAIC,kBAAS,CAACN,IAAI,EAAEC,IAAI,CAAC,GAAG,IAAI;IAC9D,KAAK,CAACC,MAAM,EAAEE,GAAG,CAAC;IAAC,KAKrBL,GAAG;IAHD,IAAI,CAACA,GAAG,GAAGA,GAAG;EAChB;EAUAQ,QAAQA,CAAA,EAAG;IACT,OAAO,KAAK,CAACA,QAAQ,CAAC,IAAI,CAACR,GAAG,CAAC;EACjC;AACF;AASA,SAASI,gBAAgBA,CACvBF,IAA6C,EAC7CD,IAAsB,EACd;EAAA,IAAAQ,qBAAA;EACR,MAAMN,MAAc,GAAG;IACrBO,sBAAsB,EAAET,IAAI,CAACS,sBAAsB;IACnDC,qBAAqB,EAAEV,IAAI,CAACU,qBAAqB;IACjDC,kBAAkB,EAAEX,IAAI,CAACW,kBAAkB;IAC3CC,WAAW,EAAEZ,IAAI,CAACY,WAAW;IAC7BC,oBAAoB,EAAEb,IAAI,CAACa,oBAAoB;IAC/CC,QAAQ,EAAEd,IAAI,CAACc,QAAQ,IAAI,IAAI,IAAId,IAAI,CAACc,QAAQ;IAChDC,OAAO,EAAEf,IAAI,CAACe,OAAO;IACrBC,QAAQ,EAAEhB,IAAI,CAACgB,QAAQ;IACvBC,OAAO,EAAEjB,IAAI,CAACiB,OAAO;IACrBC,MAAM,EAAE;MACNC,sBAAsB,EAAE,IAAI;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,WAAW,EAAAC,MAAA,CAAAC,MAAA;MACTC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAwC;IAAK,GACjD1B,IAAI,CAACqB,WAAW,CACpB;IACDM,wBAAwB,GAAAnB,qBAAA,GAAER,IAAI,CAAC2B,wBAAwB,YAAAnB,qBAAA,GAAI,MAAM;IACjEoB,UAAU,EAAE5B,IAAI,CAAC4B,UAAU;IAC3BC,uBAAuB,EAAE7B,IAAI,CAAC6B;EAChC,CAAC;EAEkC;IACjC3B,MAAM,CAAC4B,sBAAsB,GAAG9B,IAAI,CAAC8B,sBAAsB;IAC3D5B,MAAM,CAACmB,WAAW,CAACU,IAAI,GAAG/B,IAAI,CAACgC,qBAAqB;EACtD;EAEA,IAAI9B,MAAM,CAACc,QAAQ,EAAE;IACnBd,MAAM,CAACa,OAAO,GAAG,IAAI;IAErBb,MAAM,CAACS,kBAAkB,GACvBT,MAAM,CAACS,kBAAkB,KAAK,MAAMT,MAAM,CAACY,QAAQ,CAAC;EACxD,CAAC,MAAM;IACLZ,MAAM,CAACS,kBAAkB,GACvBT,MAAM,CAACS,kBAAkB,KACxBsB,KAAK,IACJ/B,MAAM,CAACY,QAAQ,IACfmB,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,IAC1BD,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAClC;EAEA,IAAIhC,MAAM,CAACa,OAAO,KAAK,MAAM,EAAE;IAC7Bb,MAAM,CAACa,OAAO,GAAG,OAAOd,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACkC,MAAM,GAAG,MAAO;IAElE,IAAIjC,MAAM,CAACa,OAAO,EAAE;MAClBqB,OAAO,CAACC,KAAK,CACX,kEAAkE,GAC/D,GAAErC,IAAI,CAACsC,QAAS,6BAA4B,OAAQ,GACzD,CAAC;IACH;EACF;EAEA,IAAIpC,MAAM,CAACa,OAAO,EAAE;IAClBb,MAAM,CAACgB,MAAM,CAACC,sBAAsB,GAAG,KAAK;EAC9C;EAEA,MAAM;IAAEV,sBAAsB;IAAEC,qBAAqB;IAAEC;EAAmB,CAAC,GACzET,MAAM;EAER,IAAIO,sBAAsB,IAAI,CAACE,kBAAkB,CAACF,sBAAsB,CAAC,EAAE;IACzEP,MAAM,CAACO,sBAAsB,GAAG8B,SAAS;EAC3C;EACA,IAAI7B,qBAAqB,IAAI,CAACC,kBAAkB,CAACD,qBAAqB,CAAC,EAAE;IACvER,MAAM,CAACQ,qBAAqB,GAAG6B,SAAS;EAC1C;EAEA,OAAOrC,MAAM;AACf;AA+HO,MAAMsC,aAAa,CAAC;EAEzB1C,WAAWA,CAACC,GAAW,EAAEC,IAAuB,EAAEC,IAAa,EAAE;IAAA,KADzDwC,UAAU;IAEhB,IAAI,CAACA,UAAU,GAAG,IAAI7C,SAAS,CAACG,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;EAClD;EACAM,QAAQA,CAAA,EAAoB;IAC1B,OAAO,IAAI,CAACkC,UAAU,CAAClC,QAAQ,CAAC,CAAC;EACnC;AACF;AAACmC,OAAA,CAAAF,aAAA,GAAAA,aAAA;AASc,SAASjC,QAAQA,CAC9BR,GAAW,EACXC,IAAuB,EACvBC,IAA8C,EAC9C;EACA,MAAM0C,GAAG,GAAG,IAAI/C,SAAS,CAACG,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;EAC1C,OAAO0C,GAAG,CAACpC,QAAQ,CAAC,CAAC;AACvB"}