// src/components/Home.js - Modern BookStore Landing Page

import React from 'react';
import { Link } from "react-router-dom";
import { FaBook, FaUsers, FaStore, FaUserShield } from 'react-icons/fa';

const Home = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Modern Navigation */}
      <nav className="bg-white/80 backdrop-blur-md shadow-lg border-b border-indigo-100 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center space-x-2 text-2xl font-bold text-indigo-700 hover:text-indigo-800 transition-colors">
              <FaBook className="text-indigo-600" />
              <span>BookStore</span>
            </Link>
            <div className="hidden md:flex items-center space-x-6">
              <Link to="/login" className="flex items-center space-x-2 px-4 py-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-lg transition-all">
                <FaUsers />
                <span>Customer</span>
              </Link>
              <Link to="/slogin" className="flex items-center space-x-2 px-4 py-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-lg transition-all">
                <FaStore />
                <span>Seller</span>
              </Link>
              <Link to="/alogin" className="flex items-center space-x-2 px-4 py-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-lg transition-all">
                <FaUserShield />
                <span>Admin</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="container mx-auto px-6 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-800 mb-6 leading-tight">
            Welcome to <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">BookStore</span>
          </h1>
          <p className="text-xl text-gray-600 mb-12 leading-relaxed">
            Discover, buy, and sell books in our modern digital marketplace.
            Connect readers with sellers in a seamless experience.
          </p>

          {/* Action Cards */}
          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <Link to="/login" className="group">
              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-indigo-100 hover:border-indigo-200 hover:-translate-y-1">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <FaUsers className="text-white text-2xl" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">Browse & Buy</h3>
                <p className="text-gray-600 leading-relaxed">
                  Explore thousands of books, add to wishlist, and purchase your favorites with ease.
                </p>
              </div>
            </Link>

            <Link to="/slogin" className="group">
              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-indigo-100 hover:border-indigo-200 hover:-translate-y-1">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <FaStore className="text-white text-2xl" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">Sell Books</h3>
                <p className="text-gray-600 leading-relaxed">
                  List your books, manage inventory, and track orders through our seller dashboard.
                </p>
              </div>
            </Link>

            <Link to="/alogin" className="group">
              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-indigo-100 hover:border-indigo-200 hover:-translate-y-1">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-violet-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <FaUserShield className="text-white text-2xl" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">Admin Panel</h3>
                <p className="text-gray-600 leading-relaxed">
                  Manage users, sellers, and oversee the entire marketplace operations.
                </p>
              </div>
            </Link>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-indigo-100 p-4">
        <div className="flex justify-around">
          <Link to="/login" className="flex flex-col items-center space-y-1 text-indigo-600">
            <FaUsers />
            <span className="text-xs">Customer</span>
          </Link>
          <Link to="/slogin" className="flex flex-col items-center space-y-1 text-indigo-600">
            <FaStore />
            <span className="text-xs">Seller</span>
          </Link>
          <Link to="/alogin" className="flex flex-col items-center space-y-1 text-indigo-600">
            <FaUserShield />
            <span className="text-xs">Admin</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home;
