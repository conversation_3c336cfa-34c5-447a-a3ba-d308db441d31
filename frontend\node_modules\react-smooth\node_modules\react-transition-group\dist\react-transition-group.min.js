!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e=e||self).ReactTransitionGroup={},e.React,e.ReactDOM)}(this,function(e,t,n){"use strict";var r="default"in t?t.default:t,o="default"in n?n.default:n;function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function a(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function s(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function l(e,t){return e(t={exports:{}},t.exports),t.exports}var p=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;(function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(t).map(function(e){return t[e]}).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach(function(e){r[e]=e}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(e){return!1}})()&&Object.assign;var d="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";function f(){}var h=l(function(e){e.exports=function(){function e(e,t,n,r,o,i){if(i!==d){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t};return n.checkPropTypes=f,n.PropTypes=n,n}()}),m=h.object,E=(h.oneOfType,h.element,h.bool,h.func,l(function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}}}));s(E);var v=l(function(e,t){t.__esModule=!0,t.default=function(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")},e.exports=t.default});s(v);var y=s(l(function(e,t){t.__esModule=!0,t.default=function(e,t){e.classList?e.classList.add(t):(0,n.default)(e,t)||("string"==typeof e.className?e.className=e.className+" "+t:e.setAttribute("class",(e.className&&e.className.baseVal||"")+" "+t))};var n=E(v);e.exports=t.default}));function x(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function g(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}function b(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=e&&this.setState(e)}function C(e){this.setState(function(t){var n=this.constructor.getDerivedStateFromProps(e,t);return null!=n?n:null}.bind(this))}function N(e,t){try{var n=this.props,r=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(n,r)}finally{this.props=n,this.state=r}}function S(e){var t=e.prototype;if(!t||!t.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof e.getDerivedStateFromProps&&"function"!=typeof t.getSnapshotBeforeUpdate)return e;var n=null,r=null,o=null;if("function"==typeof t.componentWillMount?n="componentWillMount":"function"==typeof t.UNSAFE_componentWillMount&&(n="UNSAFE_componentWillMount"),"function"==typeof t.componentWillReceiveProps?r="componentWillReceiveProps":"function"==typeof t.UNSAFE_componentWillReceiveProps&&(r="UNSAFE_componentWillReceiveProps"),"function"==typeof t.componentWillUpdate?o="componentWillUpdate":"function"==typeof t.UNSAFE_componentWillUpdate&&(o="UNSAFE_componentWillUpdate"),null!==n||null!==r||null!==o){var i=e.displayName||e.name,a="function"==typeof e.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+i+" uses "+a+" but also contains the following legacy lifecycles:"+(null!==n?"\n  "+n:"")+(null!==r?"\n  "+r:"")+(null!==o?"\n  "+o:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof e.getDerivedStateFromProps&&(t.componentWillMount=b,t.componentWillReceiveProps=C),"function"==typeof t.getSnapshotBeforeUpdate){if("function"!=typeof t.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");t.componentWillUpdate=N;var s=t.componentDidUpdate;t.componentDidUpdate=function(e,t,n){var r=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:n;s.call(this,e,t,r)}}return e}b.__suppressDeprecationWarning=!0,C.__suppressDeprecationWarning=!0,N.__suppressDeprecationWarning=!0;var O="unmounted",_="exited",T="entering",k="entered",w=function(e){function t(t,n){var r;r=e.call(this,t,n)||this;var o,i=n.transitionGroup,a=i&&!i.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?a?(o=_,r.appearStatus=T):o=k:o=t.unmountOnExit||t.mountOnEnter?O:_,r.state={status:o},r.nextCallback=null,r}a(t,e);var n=t.prototype;return n.getChildContext=function(){return{transitionGroup:null}},t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===O?{status:_}:null},n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==T&&n!==k&&(t=T):n!==T&&n!==k||(t="exiting")}this.updateStatus(!1,t)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!=typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},n.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t){this.cancelNextCallback();var n=o.findDOMNode(this);t===T?this.performEnter(n,e):this.performExit(n)}else this.props.unmountOnExit&&this.state.status===_&&this.setState({status:O})},n.performEnter=function(e,t){var n=this,r=this.props.enter,o=this.context.transitionGroup?this.context.transitionGroup.isMounting:t,i=this.getTimeouts(),a=o?i.appear:i.enter;t||r?(this.props.onEnter(e,o),this.safeSetState({status:T},function(){n.props.onEntering(e,o),n.onTransitionEnd(e,a,function(){n.safeSetState({status:k},function(){n.props.onEntered(e,o)})})})):this.safeSetState({status:k},function(){n.props.onEntered(e)})},n.performExit=function(e){var t=this,n=this.props.exit,r=this.getTimeouts();n?(this.props.onExit(e),this.safeSetState({status:"exiting"},function(){t.props.onExiting(e),t.onTransitionEnd(e,r.exit,function(){t.safeSetState({status:_},function(){t.props.onExited(e)})})})):this.safeSetState({status:_},function(){t.props.onExited(e)})},n.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},n.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},n.onTransitionEnd=function(e,t,n){this.setNextCallback(n);var r=null==t&&!this.props.addEndListener;e&&!r?(this.props.addEndListener&&this.props.addEndListener(e,this.nextCallback),null!=t&&setTimeout(this.nextCallback,t)):setTimeout(this.nextCallback,0)},n.render=function(){var e=this.state.status;if(e===O)return null;var t=this.props,n=t.children,o=g(t,["children"]);if(delete o.in,delete o.mountOnEnter,delete o.unmountOnExit,delete o.appear,delete o.enter,delete o.exit,delete o.timeout,delete o.addEndListener,delete o.onEnter,delete o.onEntering,delete o.onEntered,delete o.onExit,delete o.onExiting,delete o.onExited,"function"==typeof n)return n(e,o);var i=r.Children.only(n);return r.cloneElement(i,o)},t}(r.Component);function P(){}w.contextTypes={transitionGroup:m},w.childContextTypes={transitionGroup:function(){}},w.propTypes={},w.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:P,onEntering:P,onEntered:P,onExit:P,onExiting:P,onExited:P},w.UNMOUNTED=0,w.EXITED=1,w.ENTERING=2,w.ENTERED=3,w.EXITING=4;var j=S(w),U=function(e,t){return e&&t&&t.split(" ").forEach(function(t){return y(e,t)})},A=function(e,t){return e&&t&&t.split(" ").forEach(function(t){return r=t,void((n=e).classList?n.classList.remove(r):"string"==typeof n.className?n.className=x(n.className,r):n.setAttribute("class",x(n.className&&n.className.baseVal||"",r)));var n,r})},D=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).onEnter=function(e,n){var r=t.getClassNames(n?"appear":"enter").className;t.removeClasses(e,"exit"),U(e,r),t.props.onEnter&&t.props.onEnter(e,n)},t.onEntering=function(e,n){var r=t.getClassNames(n?"appear":"enter").activeClassName;t.reflowAndAddClass(e,r),t.props.onEntering&&t.props.onEntering(e,n)},t.onEntered=function(e,n){var r=t.getClassNames("appear").doneClassName,o=t.getClassNames("enter").doneClassName,i=n?r+" "+o:o;t.removeClasses(e,n?"appear":"enter"),U(e,i),t.props.onEntered&&t.props.onEntered(e,n)},t.onExit=function(e){var n=t.getClassNames("exit").className;t.removeClasses(e,"appear"),t.removeClasses(e,"enter"),U(e,n),t.props.onExit&&t.props.onExit(e)},t.onExiting=function(e){var n=t.getClassNames("exit").activeClassName;t.reflowAndAddClass(e,n),t.props.onExiting&&t.props.onExiting(e)},t.onExited=function(e){var n=t.getClassNames("exit").doneClassName;t.removeClasses(e,"exit"),U(e,n),t.props.onExited&&t.props.onExited(e)},t.getClassNames=function(e){var n=t.props.classNames,r="string"==typeof n,o=r?(r&&n?n+"-":"")+e:n[e];return{className:o,activeClassName:r?o+"-active":n[e+"Active"],doneClassName:r?o+"-done":n[e+"Done"]}},t}a(t,e);var n=t.prototype;return n.removeClasses=function(e,t){var n=this.getClassNames(t),r=n.className,o=n.activeClassName,i=n.doneClassName;r&&A(e,r),o&&A(e,o),i&&A(e,i)},n.reflowAndAddClass=function(e,t){t&&(e&&e.scrollTop,U(e,t))},n.render=function(){var e=i({},this.props);return delete e.classNames,r.createElement(j,i({},e,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},t}(r.Component);function R(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function W(e,n){var r=Object.create(null);return e&&t.Children.map(e,function(e){return e}).forEach(function(e){r[e.key]=function(e){return n&&t.isValidElement(e)?n(e):e}(e)}),r}function F(e,t,n){return null!=n[t]?n[t]:e.props[t]}function L(e,n,r){var o=W(e.children),i=function(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var r,o=Object.create(null),i=[];for(var a in e)a in t?i.length&&(o[a]=i,i=[]):i.push(a);var s={};for(var l in t){if(o[l])for(r=0;r<o[l].length;r++){var p=o[l][r];s[o[l][r]]=n(p)}s[l]=n(l)}for(r=0;r<i.length;r++)s[i[r]]=n(i[r]);return s}(n,o);return Object.keys(i).forEach(function(a){var s=i[a];if(t.isValidElement(s)){var l=a in n,p=a in o,c=n[a],u=t.isValidElement(c)&&!c.props.in;!p||l&&!u?p||!l||u?p&&l&&t.isValidElement(c)&&(i[a]=t.cloneElement(s,{onExited:r.bind(null,s),in:c.props.in,exit:F(s,"exit",e),enter:F(s,"enter",e)})):i[a]=t.cloneElement(s,{in:!1}):i[a]=t.cloneElement(s,{onExited:r.bind(null,s),in:!0,exit:F(s,"exit",e),enter:F(s,"enter",e)})}}),i}D.defaultProps={classNames:""},D.propTypes={};var M=Object.values||function(e){return Object.keys(e).map(function(t){return e[t]})},I=function(e){function n(t,n){var r,o=(r=e.call(this,t,n)||this).handleExited.bind(R(R(r)));return r.state={handleExited:o,firstRender:!0},r}a(n,e);var o=n.prototype;return o.getChildContext=function(){return{transitionGroup:{isMounting:!this.appeared}}},o.componentDidMount=function(){this.appeared=!0,this.mounted=!0},o.componentWillUnmount=function(){this.mounted=!1},n.getDerivedStateFromProps=function(e,n){var r,o,i=n.children,a=n.handleExited;return{children:n.firstRender?(r=e,o=a,W(r.children,function(e){return t.cloneElement(e,{onExited:o.bind(null,e),in:!0,appear:F(e,"appear",r),enter:F(e,"enter",r),exit:F(e,"exit",r)})})):L(e,i,a),firstRender:!1}},o.handleExited=function(e,t){var n=W(this.props.children);e.key in n||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState(function(t){var n=i({},t.children);return delete n[e.key],{children:n}}))},o.render=function(){var e=this.props,t=e.component,n=e.childFactory,o=g(e,["component","childFactory"]),i=M(this.state.children).map(n);return delete o.appear,delete o.enter,delete o.exit,null===t?i:r.createElement(t,o,i)},n}(r.Component);I.childContextTypes={transitionGroup:h.object.isRequired},I.propTypes={},I.defaultProps={component:"div",childFactory:function(e){return e}};var G=S(I),V=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).handleEnter=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onEnter",0,n)},t.handleEntering=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onEntering",0,n)},t.handleEntered=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onEntered",0,n)},t.handleExit=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onExit",1,n)},t.handleExiting=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onExiting",1,n)},t.handleExited=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onExited",1,n)},t}a(t,e);var o=t.prototype;return o.handleLifecycle=function(e,t,o){var i,a=this.props.children,s=r.Children.toArray(a)[t];s.props[e]&&(i=s.props)[e].apply(i,o),this.props[e]&&this.props[e](n.findDOMNode(this))},o.render=function(){var e=this.props,t=e.children,n=e.in,o=g(e,["children","in"]),i=r.Children.toArray(t),a=i[0],s=i[1];return delete o.onEnter,delete o.onEntering,delete o.onEntered,delete o.onExit,delete o.onExiting,delete o.onExited,r.createElement(G,o,n?r.cloneElement(a,{key:"first",onEnter:this.handleEnter,onEntering:this.handleEntering,onEntered:this.handleEntered}):r.cloneElement(s,{key:"second",onEnter:this.handleExit,onEntering:this.handleExiting,onEntered:this.handleExited}))},t}(r.Component);V.propTypes={},e.CSSTransition=D,e.ReplaceTransition=V,e.Transition=j,e.TransitionGroup=G,Object.defineProperty(e,"__esModule",{value:!0})});
