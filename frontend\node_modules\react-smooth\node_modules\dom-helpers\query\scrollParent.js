"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

exports.__esModule = true;
exports.default = scrollPrarent;

var _style = _interopRequireDefault(require("../style"));

var _height = _interopRequireDefault(require("./height"));

function scrollPrarent(node) {
  var position = (0, _style.default)(node, 'position'),
      excludeStatic = position === 'absolute',
      ownerDoc = node.ownerDocument;
  if (position === 'fixed') return ownerDoc || document;

  while ((node = node.parentNode) && node.nodeType !== 9) {
    var isStatic = excludeStatic && (0, _style.default)(node, 'position') === 'static',
        style = (0, _style.default)(node, 'overflow') + (0, _style.default)(node, 'overflow-y') + (0, _style.default)(node, 'overflow-x');
    if (isStatic) continue;
    if (/(auto|scroll)/.test(style) && (0, _height.default)(node) < node.scrollHeight) return node;
  }

  return document;
}

module.exports = exports["default"];