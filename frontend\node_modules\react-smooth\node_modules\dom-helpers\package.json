{"name": "dom-helpers", "version": "3.4.0", "description": "tiny modular DOM lib for ie8+", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": "jquense/dom-helpers", "license": "MIT", "main": "index.js", "keywords": ["dom-helpers", "react-component", "dom", "api", "cross-browser", "style", "event", "height", "width", "dom-helpers", "class", "classlist", "css"], "release": {"publishDir": "lib", "conventionalCommits": true}, "dependencies": {"@babel/runtime": "^7.1.2"}}